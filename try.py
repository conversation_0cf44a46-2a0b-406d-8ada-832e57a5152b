import asyncio
import base64
import io
import traceback
import time
from exceptiongroup import ExceptionGroup
import numpy as np  # Add numpy for audio processing
import struct  # For binary data handling
import pyaudio
import PIL.Image
import argparse
import os
import platform
import ctypes  # For Windows lock screen
import mss  # Add mss for screen capture

from google import genai
from google.genai import types

# from movements import move

FORMAT = pyaudio.paInt16
CHANNELS = 1
SEND_SAMPLE_RATE = 16000
RECEIVE_SAMPLE_RATE = 24000
# Adjusted chunk size for better balance between responsiveness and stability
CHUNK_SIZE = 1024  # Increased back to 1024 for better stability
# Buffer settings - increased for smoother playback
MIN_BUFFER_MS = 150  # Minimum buffer size in milliseconds (increased from 100)
TARGET_BUFFER_MS = 200  # Target buffer size in milliseconds (increased from 150)
MAX_BUFFER_MS = 300  # Maximum buffer size to prevent excessive lag (increased from 250)
# Buffer flushing parameters 
MIN_CHUNKS_TO_PLAY = 3  # Minimum number of chunks to play at once
# Buffer settings
MODEL = "models/gemini-2.0-flash-exp"

DEFAULT_MODE = "none"

lock_function = {
    "name": "lock_screen",
    "description": "Locks the computer screen when unproductive activities are detected. This function is called automatically by the model without user confirmation.",
    "parameters": {
        "type": "object",
        "properties": {
            "reason": {
                "type": "string",
                "description": "The reason for locking the screen (e.g., 'social_media', 'gaming', etc.)",
            }
        },
        "required": ["reason"],
    },
}

tools = types.Tool(function_declarations=[lock_function])

config = types.GenerateContentConfig(tools=[tools])

client = genai.Client(http_options={"api_version": "v1alpha"}, api_key="AIzaSyAXV-pxHCcVFx4rS2hXtA5iezz15wGFaqo")

# Add unproductive activities dictionary
unproductive_activities = {
    'social_media': ['facebook', 'instagram', 'twitter', 'tiktok', 'snapchat', 'reddit'],
    'gaming': ['steam', 'epic', 'game', 'unity', 'roblox', 'minecraft'],
    'entertainment': ['youtube', 'netflix', 'prime', 'hulu', 'disney+', 'twitch'],
    'chat': ['whatsapp', 'telegram', 'discord', 'messenger'],
    'shopping': ['amazon', 'ebay', 'flipkart', 'shopping']
}

# Update CONFIG to include screen input
CONFIG = types.LiveConnectConfig(
    response_modalities=[
        "audio",
    ],
    media_resolution="MEDIA_RESOLUTION_MEDIUM",
    speech_config=types.SpeechConfig(
        voice_config=types.VoiceConfig(
            prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name="Puck")
        )
    ),
    system_instruction=types.Content(
        parts=[types.Part.from_text(text="You are a strict focus assistant. Only respond in English, do not respond in any other language even if the user speaks in another language. Do not pronounce special characters. Your main task is to silently monitor the screen for unproductive activities. If you detect any unproductive activity, immediately call the lock_screen function without warning or speaking. You don't need to respond to the user unless they ask a specific question.")],
        role="user"
    ),
    tools=[tools]
)

pya = pyaudio.PyAudio()



class AudioLoop:
    def __init__(self, video_mode=DEFAULT_MODE):
        self.video_mode = video_mode

        self.audio_in_queue = None
        self.out_queue = None

        self.session = None

        self.send_text_task = None
        self.receive_audio_task = None
        self.play_audio_task = None
        
        # Flag to track if model is speaking
        self.model_speaking = False
        
        # For timestamp tracking
        self.last_speaking_change = 0
        
        # Track conversation history
        self.conversation_history = []
        self.current_text = ""
        self.screen_capture_task = None

    async def send_text(self):
        while True:
            text = await asyncio.to_thread(
                input,
                "message > ",
            )
            if text.lower() == "q":
                break
            
            # Add user text to conversation history
            if text:
                self.conversation_history.append({"role": "user", "content": text})
                
            await self.session.send(input=text or ".", end_of_turn=True)

    async def send_realtime(self):
        # Use a buffer to collect audio pieces and send larger chunks
        buffer = bytearray()
        last_send_time = time.time()
        
        while True:
            msg = await self.out_queue.get()
            
            # Only send audio data when the model is not speaking
            if not self.model_speaking:
                # Append to buffer
                buffer.extend(msg["data"])
                
                # Send buffer if it's large enough or enough time has passed
                current_time = time.time()
                if len(buffer) >= CHUNK_SIZE * 2 or (current_time - last_send_time) > 0.05:
                    if buffer:
                        # Send accumulated buffer
                        await self.session.send(input={"data": bytes(buffer), "mime_type": "audio/pcm"})
                        buffer.clear()
                        last_send_time = current_time
            else:
                # Clear buffer when model is speaking to prevent stale audio
                buffer.clear()
                # Small wait to prevent CPU hogging
                await asyncio.sleep(0.01)

    async def listen_audio(self):
        mic_info = pya.get_default_input_device_info()
        self.audio_stream = await asyncio.to_thread(
            pya.open,
            format=FORMAT,
            channels=CHANNELS,
            rate=SEND_SAMPLE_RATE,
            input=True,
            input_device_index=mic_info["index"],
            frames_per_buffer=CHUNK_SIZE,
        )
        
        if __debug__:
            kwargs = {"exception_on_overflow": False}
        else:
            kwargs = {}
            
        while True:
            # Always read from microphone to prevent buffer overflow
            data = await asyncio.to_thread(self.audio_stream.read, CHUNK_SIZE, **kwargs)
            
            # Only queue the audio for sending if the model is not speaking
            if not self.model_speaking:
                await self.out_queue.put({"data": data, "mime_type": "audio/pcm"})
            
            # Small delay to reduce CPU usage
            await asyncio.sleep(0.01)
    def get_current_temperature(self, location):
        print("function called")
        return f"The current temperature in {location} is 25°C."
    
    def lock_screen(self):
        """Lock the screen when called by the model"""
        try:
            if platform.system().lower() == 'windows':
                ctypes.windll.user32.LockWorkStation()
            elif platform.system().lower() == 'darwin':  # macOS
                os.system('pmset displaysleepnow')
            elif platform.system().lower() == 'linux':
                os.system('xdg-screensaver lock')
            return "Screen locked due to unproductive activity"
        except Exception as e:
            return f"Failed to lock screen: {str(e)}"

    async def handle_tool_call(self, tool_call):
        for fc in tool_call.function_calls:
            if fc.name == "lock_screen":
                reason = fc.args.get('reason', 'unproductive activity')
                result = self.lock_screen()
                tool_response = types.LiveClientToolResponse(
                    function_responses=[types.FunctionResponse(
                        name=fc.name,
                        id=fc.id,
                        response={'result': result},
                    )]
                )
                await self.session.send(input=tool_response)

    async def receive_audio(self):
        """Background task to read from the websocket and write pcm chunks to the output queue"""
        while True:
            turn = self.session.receive()
            
            # Reset text for this turn
            self.current_text = ""
            
            # Set model speaking flag when receiving data
            first_chunk = True
            audio_chunks_received = 0
            chunk_processing_times = []
            last_chunk_time = None
            
            async for response in turn:
                print(response.tool_call)
                now = time.time()
                tool_call = response.tool_call
                if tool_call is not None:
                    await self.handle_tool_call(tool_call) 
                if last_chunk_time:
                    chunk_processing_times.append(now - last_chunk_time)
                last_chunk_time = now
                if data := response.data:
                    if first_chunk:
                        # Set speaking flag on first chunk
                        print("\nModel started speaking")
                        self.model_speaking = True
                        self.last_speaking_change = time.time()
                        first_chunk = False
                        
                        # Make sure queue is empty at start of speech
                        while not self.audio_in_queue.empty():
                            try:
                                self.audio_in_queue.get_nowait()
                            except:
                                pass
                    
                    # Track number of chunks for debugging
                    audio_chunks_received += 1
                    
                    # Use a while loop to ensure the chunk is added to the queue
                    # This is critical to prevent missing chunks
                    chunk_added = False
                    retry_attempts = 0
                    while not chunk_added and retry_attempts < 5:
                        try:
                            # First try non-blocking for speed
                            if retry_attempts == 0:
                                self.audio_in_queue.put_nowait(data)
                                chunk_added = True
                            else:
                                # On retry, use blocking put with a timeout
                                await asyncio.wait_for(self.audio_in_queue.put(data), 0.1)
                                chunk_added = True
                        except (asyncio.QueueFull, asyncio.TimeoutError):
                            # If queue is full, make space
                            try:
                                self.audio_in_queue.get_nowait()
                                retry_attempts += 1
                            except:
                                # Last resort - small wait and retry
                                await asyncio.sleep(0.01)
                                retry_attempts += 1
                        except Exception as e:
                            print(f"Error queuing audio chunk: {e}")
                            break
                    
                    # If still failed to add chunk after retries, log it
                    if not chunk_added:
                        print(f"WARNING: Failed to add audio chunk after {retry_attempts} attempts")
                    
                    continue
                    
                if text := response.text:
                    print(text, end="")
                    self.current_text += text

            # Add model response to conversation history
            if self.current_text:
                self.conversation_history.append({"role": "assistant", "content": self.current_text})
            
            # Calculate and display chunk processing statistics
            if chunk_processing_times:
                avg_time = sum(chunk_processing_times) / len(chunk_processing_times)
                max_time = max(chunk_processing_times)
                print(f"\nEnd of turn (processed {audio_chunks_received} audio chunks, avg time: {avg_time:.3f}s, max: {max_time:.3f}s)")
            else:
                print(f"\nEnd of turn (processed {audio_chunks_received} audio chunks)")
            
            # Wait to ensure last audio chunk is fully played
            # Increased to give more time for final chunks to play
            await asyncio.sleep(0.3)
            
            # Drain the queue more carefully - keep a counter
            drained_chunks = 0
            while not self.audio_in_queue.empty():
                try:
                    self.audio_in_queue.get_nowait()
                    drained_chunks += 1
                except:
                    break
            
            if drained_chunks > 0:
                print(f"Drained {drained_chunks} remaining audio chunks")
            
            # Update speaking status with timestamp
            self.model_speaking = False
            self.last_speaking_change = time.time()
            print("Ready for next input")

    async def play_audio(self):
        """Play received audio with advanced buffer management to prevent lost words and pauses"""
        # Calculate buffer sizes in bytes
        bytes_per_ms = (RECEIVE_SAMPLE_RATE * 2) / 1000  # 2 bytes per sample for FORMAT=paInt16
        min_buffer_bytes = int(MIN_BUFFER_MS * bytes_per_ms)
        target_buffer_bytes = int(TARGET_BUFFER_MS * bytes_per_ms)
        max_buffer_bytes = int(MAX_BUFFER_MS * bytes_per_ms)
        
        # Open audio stream with optimized parameters
        stream = await asyncio.to_thread(
            pya.open,
            format=FORMAT,
            channels=CHANNELS,
            rate=RECEIVE_SAMPLE_RATE,
            output=True,
            frames_per_buffer=CHUNK_SIZE,
            output_device_index=pya.get_default_output_device_info()["index"]
        )
        
        print(f"Audio buffer configuration: min={MIN_BUFFER_MS}ms, target={TARGET_BUFFER_MS}ms, max={MAX_BUFFER_MS}ms")
        print(f"Buffer sizes in bytes: min={min_buffer_bytes}, target={target_buffer_bytes}, max={max_buffer_bytes}")
        
        # Main audio buffer
        audio_buffer = bytearray()
        
        # State tracking
        last_chunk_time = time.time()
        playback_stats = {
            "chunks_played": 0,
            "buffer_underruns": 0,
            "buffer_overruns": 0
        }
        
        # Dynamic buffer adjustment
        current_buffer_target = target_buffer_bytes
        silence_detector = []  # Track recent volumes for silence detection
        consecutive_silence_frames = 0
        
        # Audio session state
        is_continuous_speech = False
        speech_pause_detected = False
        
        while True:
            try:
                # Check if we've received no audio for too long (possible end of sentence)
                current_time = time.time()
                time_since_last_chunk = current_time - last_chunk_time
                
                # Try to get the next chunk with a timeout
                try:
                    # Use a short timeout for responsiveness
                    bytestream = await asyncio.wait_for(self.audio_in_queue.get(), 0.05)
                    last_chunk_time = time.time()
                    
                    # Marker for continuous speech
                    if not is_continuous_speech and len(audio_buffer) > 0:
                        is_continuous_speech = True
                    
                    # Try to detect silence/pauses in the audio
                    try:
                        samples = np.frombuffer(bytestream, dtype=np.int16)
                        if len(samples) > 0:
                            # RMS volume
                            volume = np.sqrt(np.mean(samples.astype(np.float32)**2))
                            silence_detector.append(volume)
                            # Keep only recent samples
                            if len(silence_detector) > 10:
                                silence_detector.pop(0)
                            
                            # Check for silence (natural pause in speech)
                            is_silence = volume < 150  # Threshold for silence
                            if is_silence:
                                consecutive_silence_frames += 1
                            else:
                                consecutive_silence_frames = 0
                                
                            # If enough consecutive silence frames, mark as pause
                            speech_pause_detected = consecutive_silence_frames >= 3
                    except Exception:
                        # If volume detection fails, ignore it
                        pass
                    
                    # Add to buffer
                    audio_buffer.extend(bytestream)
                    
                    # Calculate current conditions for playback decision
                    buffer_size = len(audio_buffer)
                    queue_size = self.audio_in_queue.qsize()
                    buffer_ratio = buffer_size / current_buffer_target
                    
                    # Logic for when to play audio
                    should_play = False
                    
                    # Play immediately if buffer is over max size
                    if buffer_size >= max_buffer_bytes:
                        should_play = True
                        playback_stats["buffer_overruns"] += 1
                    # Play immediately if we detect a pause in speech and have enough data
                    elif speech_pause_detected and buffer_size >= min_buffer_bytes:
                        should_play = True
                    # Play if we reached our target buffer size
                    elif buffer_size >= current_buffer_target:
                        should_play = True
                    # Play if queue is empty and we have enough buffered data
                    elif queue_size == 0 and buffer_size >= min_buffer_bytes:
                        should_play = True
                        # Small wait to see if more data arrives
                        await asyncio.sleep(0.01)
                        if self.audio_in_queue.empty():
                            should_play = True
                        else:
                            should_play = False
                    # Safety check - if buffer getting too large
                    elif buffer_size >= target_buffer_bytes * 1.5:
                        should_play = True
                    
                    # Play the buffered audio if conditions are met
                    if should_play and buffer_size > 0:
                        try:
                            # For smoother playback, normalize audio volume
                            try:
                                audio_np = np.frombuffer(audio_buffer, dtype=np.int16)
                                # Only normalize if we have enough samples
                                if len(audio_np) > 100:
                                    # Get current RMS value
                                    rms = np.sqrt(np.mean(audio_np.astype(np.float32)**2))
                                    
                                    # Only normalize if volume is too low or too high
                                    if rms < 1000 or rms > 10000:
                                        # Target RMS around 5000 for good volume
                                        target_rms = 5000
                                        # Calculate gain with limiting to avoid extreme values
                                        gain = min(max(target_rms / max(rms, 1.0), 0.5), 3.0)
                                        
                                        # Apply gain
                                        audio_np = audio_np.astype(np.float32) * gain
                                        # Soft limiting to avoid clipping
                                        audio_np = np.tanh(audio_np / 32767.0) * 32767.0
                                        # Back to int16
                                        audio_np = audio_np.astype(np.int16)
                                        
                                        # Create bytes from numpy array
                                        # Convert to bytes explicitly to fix the error
                                        processed_audio = bytes(audio_np.tobytes())
                                    else:
                                        # Volume is already good - convert to bytes
                                        processed_audio = bytes(audio_buffer)
                                else:
                                    # Convert bytearray to bytes for small buffers
                                    processed_audio = bytes(audio_buffer)
                            except Exception as e:
                                # Fall back to raw audio if normalization fails, but ensure it's bytes
                                processed_audio = bytes(audio_buffer)
                                print(f"Volume normalization skipped: {e}")
                                
                            # Play the audio - now always using bytes, not bytearray
                            await asyncio.to_thread(stream.write, processed_audio)
                            playback_stats["chunks_played"] += 1
                            
                            # Clear buffer after playing
                            audio_buffer = bytearray()
                            is_continuous_speech = False
                            speech_pause_detected = False
                            consecutive_silence_frames = 0
                            
                            # Adaptive buffer sizing based on queue state and performance
                            if queue_size > 10:
                                # Many chunks waiting - reduce buffer target to catch up
                                current_buffer_target = max(min_buffer_bytes, int(target_buffer_bytes * 0.8))
                            elif queue_size < 3:
                                # Few chunks waiting - increase buffer for smoother playback
                                current_buffer_target = min(max_buffer_bytes, int(target_buffer_bytes * 1.2))
                            else:
                                # Reset to default
                                current_buffer_target = target_buffer_bytes
                        except Exception as e:
                            print(f"Error playing audio: {e}")
                            audio_buffer = bytearray()  # Clear on critical error
                
                except asyncio.TimeoutError:
                    # Handle timeout - if we have data and waited long enough, play it
                    if len(audio_buffer) >= min_buffer_bytes and time_since_last_chunk > 0.2:
                        try:
                            # Convert bytearray to bytes before passing to stream.write
                            await asyncio.to_thread(stream.write, bytes(audio_buffer))
                            audio_buffer = bytearray()
                            playback_stats["buffer_underruns"] += 1
                        except Exception as e:
                            print(f"Error in timeout playback: {e}")
                            audio_buffer = bytearray()
                    
                    # If we have very little data and waited a long time, clear buffer
                    if len(audio_buffer) < min_buffer_bytes and time_since_last_chunk > 1.0:
                        audio_buffer = bytearray()
                    
                    continue
            
            except Exception as e:
                print(f"Unexpected error in audio playback: {e}")
                # Don't clear buffer on non-critical errors
                # Only trim if it's getting too large
                if len(audio_buffer) > max_buffer_bytes * 2:
                    # Keep the most recent data
                    audio_buffer = audio_buffer[-max_buffer_bytes:]

    def _get_screen(self):
        """Capture screen content"""
        try:
            with mss.mss() as sct:
                monitor = sct.monitors[0]
                screenshot = sct.grab(monitor)
                image_bytes = mss.tools.to_png(screenshot.rgb, screenshot.size)
                img = PIL.Image.open(io.BytesIO(image_bytes))
                img = img.convert('RGB')
                img.thumbnail((800, 600))  # Reduce size for better performance

                image_io = io.BytesIO()
                img.save(image_io, format="jpeg", quality=85)
                image_io.seek(0)

                return {
                    "mime_type": "image/jpeg",
                    "data": base64.b64encode(image_io.read()).decode()
                }
        except Exception as e:
            print(f"Error capturing screen: {e}")
            return None

    async def capture_screen(self):
        """Continuously capture and send screen content"""
        while True:
            try:
                frame = await asyncio.to_thread(self._get_screen)
                if frame:
                    # Send screen content without waiting for user interaction
                    await self.session.send(input=frame, end_of_turn=True)
                await asyncio.sleep(0.5)  # Check screen every 0.5 seconds
            except Exception as e:
                print(f"Error in screen capture: {e}")
                await asyncio.sleep(1)

    async def run(self):
        try:
            async with (
                client.aio.live.connect(model=MODEL, config=CONFIG) as session,
                asyncio.TaskGroup() as tg,
            ):
                self.session = session

                self.audio_in_queue = asyncio.Queue(maxsize=200)
                self.out_queue = asyncio.Queue(maxsize=20)
                
                print(f"Starting focus monitoring session - Screen monitoring active")
                
                # Start screen capture task first to ensure immediate monitoring
                screen_capture_task = tg.create_task(self.capture_screen())
                
                # Start other tasks
                play_audio_task = tg.create_task(self.play_audio())
                receive_task = tg.create_task(self.receive_audio())
                send_realtime_task = tg.create_task(self.send_realtime())
                listen_task = tg.create_task(self.listen_audio())
                send_text_task = tg.create_task(self.send_text())

                await send_text_task
                raise asyncio.CancelledError("User requested exit")

        except asyncio.CancelledError:
            pass
        except ExceptionGroup as EG:
            if hasattr(self, 'audio_stream'):
                self.audio_stream.close()
            traceback.print_exception(EG)
        finally:
            print("Shutting down audio system...")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--mode",
        type=str,
        default=DEFAULT_MODE,
        help="Mode of operation (audio only)",
        choices=["none"],  # Updated choices to only "none"
    )
    args = parser.parse_args()
    main = AudioLoop(video_mode=args.mode)
    asyncio.run(main.run())
