import asyncio
import base64
import io
import traceback
from exceptiongroup import ExceptionGroup
import pyaudio
import PIL.Image
import mss

from google import genai
from google.genai import types

# Audio settings
FORMAT = pyaudio.paInt16
CHANNELS = 1
SEND_SAMPLE_RATE = 16000
RECEIVE_SAMPLE_RATE = 24000
CHUNK_SIZE = 1024

MODEL = "models/gemini-2.0-flash-exp"

# Replace with your actual API key
client = genai.Client(http_options={"api_version": "v1alpha"}, api_key="AIzaSyBW2rAd__6HlW6eUTwyH0mKO_xMfSeggf8")

# Configuration for screen analysis and voice interaction
CONFIG = types.LiveConnectConfig(
    response_modalities=[
        "audio",
    ],
    media_resolution="MEDIA_RESOLUTION_MEDIUM",
    speech_config=types.SpeechConfig(
        voice_config=types.VoiceConfig(
            prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name="Puck")
        )
    ),
    system_instruction=types.Content(
        parts=[types.Part.from_text(text="You are a helpful voice assistant that can analyze screen content and answer user questions. When the user asks you to analyze the screen or asks questions about what's on the screen, provide detailed and helpful responses. Always respond in a conversational and friendly manner.")],
        role="user"
    )
)

pya = pyaudio.PyAudio()


class VoiceAssistant:
    def __init__(self):
        self.audio_in_queue = None
        self.out_queue = None
        self.session = None
        self.model_speaking = False

    async def send_realtime(self):
        """Send audio data to the model"""
        while True:
            msg = await self.out_queue.get()
            
            # Only send audio data when the model is not speaking
            if not self.model_speaking:
                await self.session.send(input={"data": msg["data"], "mime_type": "audio/pcm"})
            
            await asyncio.sleep(0.01)

    async def listen_audio(self):
        """Capture audio from microphone"""
        mic_info = pya.get_default_input_device_info()
        self.audio_stream = await asyncio.to_thread(
            pya.open,
            format=FORMAT,
            channels=CHANNELS,
            rate=SEND_SAMPLE_RATE,
            input=True,
            input_device_index=mic_info["index"],
            frames_per_buffer=CHUNK_SIZE,
        )
        
        while True:
            data = await asyncio.to_thread(self.audio_stream.read, CHUNK_SIZE, exception_on_overflow=False)
            
            # Only queue the audio for sending if the model is not speaking
            if not self.model_speaking:
                await self.out_queue.put({"data": data, "mime_type": "audio/pcm"})
            
            await asyncio.sleep(0.01)

    async def receive_audio(self):
        """Receive and process audio responses from the model"""
        while True:
            turn = self.session.receive()
            
            # Set model speaking flag when receiving data
            first_chunk = True
            
            async for response in turn:
                if data := response.data:
                    if first_chunk:
                        print("\nAssistant is speaking...")
                        self.model_speaking = True
                        first_chunk = False
                        
                        # Clear audio queue at start of speech
                        while not self.audio_in_queue.empty():
                            try:
                                self.audio_in_queue.get_nowait()
                            except:
                                pass
                    
                    # Add audio data to playback queue
                    await self.audio_in_queue.put(data)
                    
                if text := response.text:
                    print(text, end="")
            
            print("\nReady for next input")
            self.model_speaking = False

    async def play_audio(self):
        """Play received audio"""
        stream = await asyncio.to_thread(
            pya.open,
            format=FORMAT,
            channels=CHANNELS,
            rate=RECEIVE_SAMPLE_RATE,
            output=True,
            frames_per_buffer=CHUNK_SIZE,
        )
        
        while True:
            try:
                bytestream = await self.audio_in_queue.get()
                await asyncio.to_thread(stream.write, bytestream)
            except Exception as e:
                print(f"Error playing audio: {e}")

    def _get_screen(self):
        """Capture screen content"""
        try:
            with mss.mss() as sct:
                monitor = sct.monitors[0]
                screenshot = sct.grab(monitor)
                image_bytes = mss.tools.to_png(screenshot.rgb, screenshot.size)
                img = PIL.Image.open(io.BytesIO(image_bytes))
                img = img.convert('RGB')
                img.thumbnail((800, 600))  # Reduce size for better performance

                image_io = io.BytesIO()
                img.save(image_io, format="jpeg", quality=85)
                image_io.seek(0)

                return {
                    "mime_type": "image/jpeg",
                    "data": base64.b64encode(image_io.read()).decode()
                }
        except Exception as e:
            print(f"Error capturing screen: {e}")
            return None

    async def capture_screen_periodically(self):
        """Capture screen periodically to provide context"""
        while True:
            try:
                # Capture screen every 3 seconds when model is not speaking
                if not self.model_speaking:
                    frame = await asyncio.to_thread(self._get_screen)
                    if frame:
                        await self.session.send(input=frame, end_of_turn=True)

                await asyncio.sleep(3.0)  # Capture every 3 seconds
            except Exception as e:
                print(f"Error in screen capture: {e}")
                await asyncio.sleep(1)

    async def run(self):
        try:
            async with (
                client.aio.live.connect(model=MODEL, config=CONFIG) as session,
                asyncio.TaskGroup() as tg,
            ):
                self.session = session

                self.audio_in_queue = asyncio.Queue(maxsize=100)
                self.out_queue = asyncio.Queue(maxsize=20)
                
                print("Voice Assistant started! Speak to interact.")
                print("The assistant can see your screen and answer questions about it.")
                
                # Start all tasks
                tg.create_task(self.play_audio())
                tg.create_task(self.receive_audio())
                tg.create_task(self.send_realtime())
                tg.create_task(self.listen_audio())
                tg.create_task(self.capture_screen_periodically())
                
                # Keep running until interrupted
                await asyncio.sleep(float('inf'))

        except KeyboardInterrupt:
            print("\nShutting down...")
        except ExceptionGroup as EG:
            if hasattr(self, 'audio_stream'):
                self.audio_stream.close()
            traceback.print_exception(EG)
        finally:
            print("Voice Assistant stopped.")


if __name__ == "__main__":
    assistant = VoiceAssistant()
    asyncio.run(assistant.run())
