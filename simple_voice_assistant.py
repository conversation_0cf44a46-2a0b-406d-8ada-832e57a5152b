import asyncio
import base64
import io
import traceback
import time
from exceptiongroup import ExceptionGroup
import pyaudio
import PIL.Image
import mss

from google import genai
from google.genai import types

# Audio settings
FORMAT = pyaudio.paInt16
CHANNELS = 1
SEND_SAMPLE_RATE = 16000
RECEIVE_SAMPLE_RATE = 24000
CHUNK_SIZE = 1024

MODEL = "models/gemini-2.0-flash-exp"

# API Key - Replace with your actual API key
API_KEY = "AIzaSyAu5_X6Laf5XMlzyAdLFGd5Uxh62hKl2lA"

# Initialize client with timeout settings
client = genai.Client(
    http_options={
        "api_version": "v1alpha",
        "timeout": 30.0  # 30 second timeout
    },
    api_key=API_KEY
)

# Configuration for screen analysis and voice interaction
CONFIG = types.LiveConnectConfig(
    response_modalities=[
        "audio",
    ],
    media_resolution="MEDIA_RESOLUTION_MEDIUM",
    speech_config=types.SpeechConfig(
        voice_config=types.VoiceConfig(
            prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name="Puck")
        )
    ),
    system_instruction=types.Content(
        parts=[types.Part.from_text(text="You are a helpful voice assistant. When the user asks you to analyze the screen or asks questions about what's visible, I will provide you with a screenshot. Answer their questions based on what you can see. Be conversational and helpful.")],
        role="user"
    )
)

pya = pyaudio.PyAudio()


class SimpleVoiceAssistant:
    def __init__(self):
        self.audio_in_queue = None
        self.out_queue = None
        self.session = None
        self.model_speaking = False
        self.should_capture_screen = False

    async def test_connection(self):
        """Test API connection before starting main loop"""
        try:
            print("🔍 Testing API connection...")
            # Simple test to verify API key and connection
            test_client = genai.Client(api_key=API_KEY)
            # This will fail quickly if API key is invalid
            await asyncio.to_thread(lambda: list(test_client.models.list()))
            print("✅ API connection successful!")
            return True
        except Exception as e:
            print(f"❌ API connection failed: {e}")
            print("Please check your API key and internet connection.")
            return False

    async def send_realtime(self):
        """Send audio data to the model"""
        while True:
            msg = await self.out_queue.get()
            
            # Only send audio data when the model is not speaking
            if not self.model_speaking:
                await self.session.send(input={"data": msg["data"], "mime_type": "audio/pcm"})
            
            await asyncio.sleep(0.01)

    async def listen_audio(self):
        """Capture audio from microphone"""
        try:
            mic_info = pya.get_default_input_device_info()
            print(f"🎤 Using microphone: {mic_info['name']}")

            self.audio_stream = await asyncio.to_thread(
                pya.open,
                format=FORMAT,
                channels=CHANNELS,
                rate=SEND_SAMPLE_RATE,
                input=True,
                input_device_index=mic_info["index"],
                frames_per_buffer=CHUNK_SIZE,
            )

            while True:
                try:
                    data = await asyncio.to_thread(self.audio_stream.read, CHUNK_SIZE, exception_on_overflow=False)

                    # Only queue the audio for sending if the model is not speaking
                    if not self.model_speaking:
                        await self.out_queue.put({"data": data, "mime_type": "audio/pcm"})

                    await asyncio.sleep(0.01)
                except Exception as e:
                    print(f"⚠️  Audio input error: {e}")
                    await asyncio.sleep(0.1)

        except Exception as e:
            print(f"❌ Failed to initialize microphone: {e}")
            print("⏳ Retrying microphone connection in 5 seconds...")
            await asyncio.sleep(5)
            # Retry microphone connection instead of stopping
            await self.listen_audio()

    async def receive_audio(self):
        """Receive and process audio responses from the model"""
        while True:
            turn = self.session.receive()
            
            # Set model speaking flag when receiving data
            first_chunk = True
            
            async for response in turn:
                if data := response.data:
                    if first_chunk:
                        print("\nAssistant is speaking...")
                        self.model_speaking = True
                        first_chunk = False
                        
                        # Clear audio queue at start of speech
                        while not self.audio_in_queue.empty():
                            try:
                                self.audio_in_queue.get_nowait()
                            except:
                                pass
                    
                    # Add audio data to playback queue
                    await self.audio_in_queue.put(data)
                    
                if text := response.text:
                    print(text, end="")
            
            print("\nReady for next input")
            self.model_speaking = False

    async def play_audio(self):
        """Play received audio"""
        try:
            speaker_info = pya.get_default_output_device_info()
            print(f"🔊 Using speaker: {speaker_info['name']}")

            stream = await asyncio.to_thread(
                pya.open,
                format=FORMAT,
                channels=CHANNELS,
                rate=RECEIVE_SAMPLE_RATE,
                output=True,
                frames_per_buffer=CHUNK_SIZE,
            )

            while True:
                try:
                    bytestream = await self.audio_in_queue.get()
                    await asyncio.to_thread(stream.write, bytestream)
                except Exception as e:
                    print(f"⚠️  Audio playback error: {e}")
                    await asyncio.sleep(0.1)

        except Exception as e:
            print(f"❌ Failed to initialize speakers: {e}")
            print("⏳ Retrying speaker connection in 5 seconds...")
            await asyncio.sleep(5)
            # Retry speaker connection instead of stopping
            await self.play_audio()

    def _get_screen(self):
        """Capture screen content"""
        try:
            with mss.mss() as sct:
                monitor = sct.monitors[0]
                screenshot = sct.grab(monitor)
                image_bytes = mss.tools.to_png(screenshot.rgb, screenshot.size)
                img = PIL.Image.open(io.BytesIO(image_bytes))
                img = img.convert('RGB')
                img.thumbnail((800, 600))  # Reduce size for better performance

                image_io = io.BytesIO()
                img.save(image_io, format="jpeg", quality=85)
                image_io.seek(0)

                return {
                    "mime_type": "image/jpeg",
                    "data": base64.b64encode(image_io.read()).decode()
                }
        except Exception as e:
            print(f"Error capturing screen: {e}")
            return None

    async def handle_screen_requests(self):
        """Monitor for screen-related requests and capture when needed"""
        while True:
            try:
                # Check if we should capture screen (when user starts speaking)
                if not self.model_speaking:
                    # Wait a bit for user to speak
                    await asyncio.sleep(0.5)
                    
                    # Capture screen to provide context for user's question
                    frame = await asyncio.to_thread(self._get_screen)
                    if frame:
                        await self.session.send(input=frame, end_of_turn=True)
                        await asyncio.sleep(2)  # Wait before next capture
                else:
                    await asyncio.sleep(0.1)
            except Exception as e:
                print(f"Error in screen handling: {e}")
                await asyncio.sleep(1)

    async def run(self):
        retry_delay = 5
        attempt = 0

        while True:  # Infinite retry loop - never give up!
            try:
                print(f"Connecting to Gemini API... (Attempt {attempt + 1})")

                async with (
                    client.aio.live.connect(model=MODEL, config=CONFIG) as session,
                    asyncio.TaskGroup() as tg,
                ):
                    self.session = session

                    self.audio_in_queue = asyncio.Queue(maxsize=100)
                    self.out_queue = asyncio.Queue(maxsize=20)

                    print("✅ Simple Voice Assistant started successfully!")
                    print("🎤 Speak to the assistant and ask questions about your screen.")
                    print("🖥️  Screen monitoring is active.")
                    print("⏹️  Press Ctrl+C to exit.")

                    # Start all tasks
                    tg.create_task(self.play_audio())
                    tg.create_task(self.receive_audio())
                    tg.create_task(self.send_realtime())
                    tg.create_task(self.listen_audio())
                    tg.create_task(self.handle_screen_requests())

                    # Keep running until interrupted
                    await asyncio.sleep(float('inf'))

            except KeyboardInterrupt:
                print("\n🛑 Shutting down...")
                break
            except (TimeoutError, ConnectionError, OSError) as e:
                print(f"❌ Connection failed: {e}")
                if attempt < max_retries - 1:
                    print(f"⏳ Retrying in {retry_delay} seconds...")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    print("❌ Max retries reached. Waiting 30 seconds before trying again...")
                    print("   Checking: 1. Internet connection 2. API key 3. Gemini service")
                    await asyncio.sleep(30)
                    # Reset retry counter and try again instead of stopping
                    attempt = -1  # Will be incremented to 0 in next loop iteration
                    retry_delay = 5
            except ExceptionGroup as EG:
                if hasattr(self, 'audio_stream'):
                    self.audio_stream.close()
                print("❌ Error occurred:")
                traceback.print_exception(EG)
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                traceback.print_exc()
                break

        print("🔴 Voice Assistant stopped.")


async def main():
    """Main function with connection testing"""
    assistant = SimpleVoiceAssistant()

    # Test connection first
    if not await assistant.test_connection():
        print("❌ Cannot start assistant due to connection issues.")
        return

    # Start the assistant
    await assistant.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        traceback.print_exc()
